"use client";

import { A<PERSON>y<PERSON><PERSON><PERSON>, FieldArray, Form, Formik } from "formik";
import * as Yup from "yup";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import {
  createDocumentSet,
  updateDocumentSet,
  DocumentSetCreationRequest,
} from "./lib";
import { ConnectorStatus, DocumentSet, UserTeams, UserRole } from "@/lib/types";
import { TextFormField } from "@/components/admin/connectors/Field";
import { ConnectorTitle } from "@/components/admin/connectors/ConnectorTitle";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { PrivacyToggle } from "@/components/PrivacyToggle";
import React, { useEffect, useState } from "react";
import { useUser } from "@/components/user/UserProvider";

interface SetCreationPopupProps {
  ccPairs: ConnectorStatus<any, any>[];
  user_teams: UserTeams[] | undefined;
  onClose: () => void;
  setPopup: (popupSpec: PopupSpec | null) => void;
  existingDocumentSet?: DocumentSet;
}

export const DocumentSetCreationForm = ({
  ccPairs,
  user_teams,
  onClose,
  setPopup,
  existingDocumentSet,
}: SetCreationPopupProps) => {
  const isUpdate = existingDocumentSet !== undefined;
  const [localCcPairs] = useState(ccPairs);
  const [searchQuery, setSearchQuery] = useState(""); //Search query
  const { user } = useUser();

  /**
   * Filter connectors based on document set privacy settings, selected teams, and user role
   * - ADMIN users with public document sets: Show ONLY public connectors
   * - ADMIN users with private document sets: Show public + private connectors assigned to selected teams
   * - TEAM_ADMIN users (both public and private document sets): Show ONLY private connectors assigned to their teams (NO public ones)
   */
  const getFilteredConnectors = (connectors: ConnectorStatus<any, any>[], isPublic: boolean, selectedTeams: number[]) => {
    // Special handling for TEAM_ADMIN users - they should only see private connectors available to them
    if (user?.role === UserRole.TEAM_ADMIN) {
      const currentUserTeamIds = user_teams?.map(team => team.id) || [];

      // For both public and private document sets created by team admins: show ONLY their team's private connectors
      return connectors.filter(connector => {
        return connector.access_type === "private" &&
               connector.groups &&
               connector.groups.some(groupId => currentUserTeamIds.includes(groupId));
      });
    }

    // For ADMIN users
    if (isPublic) {
      // For public document sets: only show public connectors
      // This ensures public document sets can only use public data sources
      return connectors.filter(connector => connector.access_type === "public");
    }

    if (selectedTeams.length === 0) {
      // For private document sets with no teams selected: show all connectors (fallback)
      return connectors;
    }

    // For private document sets with teams selected: show public connectors + private connectors assigned to selected teams
    // This allows private document sets to access both public data and team-specific private data
    return connectors.filter(connector => {
      return connector.access_type === "public" ||
             (connector.groups && connector.groups.some(groupId => selectedTeams.includes(groupId)));
    });
  };

  useEffect(() => {
    if (existingDocumentSet?.is_public) {
      return;
    }
  }, [existingDocumentSet?.is_public]);

  return (
    <div>
      <Formik<DocumentSetCreationRequest>
        initialValues={{
          name: existingDocumentSet?.name ?? "",
          description: existingDocumentSet?.description ?? "",
          cc_pair_ids:
            existingDocumentSet?.cc_pair_descriptors.map(
              (ccPairDescriptor) => ccPairDescriptor.id
            ) ?? [],
          is_public: existingDocumentSet?.is_public ?? (
            // Role-based initial values: TEAM_ADMIN and BASIC users default to private
            user?.role === UserRole.TEAM_ADMIN  ? false : true
          ),
          user_teams: existingDocumentSet?.user_teams ?? (
            // Auto-populate teams for TEAM_ADMIN and BASIC users
            user?.role === UserRole.TEAM_ADMIN 
              ? user_teams?.map(team => team.id) ?? []
              : []
          ),
          groups: existingDocumentSet?.groups ?? [],
          users: existingDocumentSet?.users ?? [],
          // Add selectedTeams for PrivacyToggle compatibility
          selectedTeams: existingDocumentSet?.user_teams ?? (
            user?.role === UserRole.TEAM_ADMIN 
              ? (user_teams?.map(team => team.id) ?? [])
              : []
          ),
        }}
        validationSchema={Yup.object().shape({
          name: Yup.string().required("Please enter a name for the set"),
          description: Yup.string().optional(),
          cc_pair_ids: Yup.array()
            .of(Yup.number().required())
            .required("Please select at least one connector"),
        })}
        onSubmit={async (values, formikHelpers) => {
          formikHelpers.setSubmitting(true);
          // If the document set is public, then we don't want to send any groups
          const processedValues = {
            ...values,
            user_teams: values.is_public ? [] : (
              // For TEAM_ADMIN users, send empty array - backend will auto-populate
              user?.role === UserRole.TEAM_ADMIN 
                ? []
                : (values.selectedTeams || values.user_teams)
            ),
            groups: values.is_public ? [] : values.groups,
          };

          let response;
          if (isUpdate) {
            response = await updateDocumentSet({
              id: existingDocumentSet.id,
              ...processedValues,
              user_teams: processedValues.user_teams,
            });
          } else {
            response = await createDocumentSet(processedValues);
          }
          formikHelpers.setSubmitting(false);
          if (response.ok) {
            setPopup({
              message: isUpdate
                ? "Successfully updated document set!"
                : "Successfully created document set!",
              type: "success",
            });
            onClose();
          } else {
            const errorMsg = await response.text();
            setPopup({
              message: isUpdate
                ? `Error updating document set - ${errorMsg}`
                : `Error creating document set - ${errorMsg}`,
              type: "error",
            });
          }
        }}
      >
        {(props) => {
          return (
            <Form>
              <TextFormField
                name="name"
                label="Name:"
                placeholder="A name for the document set"
                disabled={isUpdate}
                autoCompleteDisabled={true}
              />
              <TextFormField
                name="description"
                label="Description:"
                placeholder="Describe what the document set represents"
                autoCompleteDisabled={true}
                optional={true}
              />

              {/* Role-based Privacy Toggle - only show for ADMIN users */}
              {user?.role === UserRole.ADMIN && (
                <PrivacyToggle
                  formikProps={props}
                  userTeams={user_teams}
                  objectName="document set"
                  setPopup={setPopup}
                  singleTeamMode={true}
                />
              )}

              <Separator />

              {user?.role === UserRole.CURATOR ? (
                <>
                  <div className="flex flex-col gap-y-1">
                    <h2 className="mb-1 font-medium text-base">
                      These are the connectors available to{" "}
                      {user_teams && user_teams.length > 1
                        ? "the selected teams"
                        : "the group you curate"}
                      :
                    </h2>

                    <p className="mb-3 text-xs">
                      All documents indexed by the selected connectors will be a part of this document set.
                    </p>

                    <input
                      type="text"
                      placeholder="Search connectors..."
                      className="mb-3 w-full px-3 py-2 text-sm border rounded-md"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <FieldArray
                      name="cc_pair_ids"
                      render={(arrayHelpers: ArrayHelpers) => {
                        // Apply privacy-based filtering first (based on document set privacy and team selection)
                        // Then apply search filtering for user convenience
                        const privacyFilteredCcPairs = getFilteredConnectors(ccPairs, props.values.is_public, props.values.selectedTeams || []);
                        const filteredCcPairs = privacyFilteredCcPairs.filter((ccPair) =>
                          (ccPair.name ?? "").toLowerCase().includes(searchQuery.toLowerCase())
                        );

                        return (
                          <div className="mb-3 flex gap-2 flex-wrap">
                            {filteredCcPairs.map((ccPair) => {
                              const ind = props.values.cc_pair_ids.indexOf(ccPair.cc_pair_id);
                              const isSelected = ind !== -1;

                              return (
                                <div
                                  key={`${ccPair.connector.id}-${ccPair.credential.id}`}
                                  className={
                                    `
                                  px-3 
                                  py-1
                                  rounded-lg 
                                  border
                                  border-border 
                                  w-fit 
                                  flex 
                                  cursor-pointer ` +
                                    (isSelected
                                      ? " bg-background-200"
                                      : " hover:bg-accent-background-hovered")
                                  }
                                  onClick={() => {
                                    if (isSelected) {
                                      arrayHelpers.remove(ind);
                                    } else {
                                      arrayHelpers.push(ccPair.cc_pair_id);
                                    }
                                  }}
                                >
                                  <div className="my-auto">
                                    <ConnectorTitle
                                      connector={ccPair.connector}
                                      ccPairId={ccPair.cc_pair_id}
                                      ccPairName={ccPair.name}
                                      isLink={false}
                                      showMetadata={false}
                                    />
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        );
                      }}
                    />
                  </div>

                  <div>
                    <FieldArray
                      name="cc_pair_ids"
                      render={() => {
                        // Filter non-visible cc pairs
                        const nonVisibleCcPairs = localCcPairs.filter(
                          (ccPair) =>
                            !(ccPair.access_type === "public") &&
                            (ccPair.groups.length === 0 ||
                              !props.values.groups.every((group) =>
                                ccPair.groups.includes(group)
                              ))
                        );

                        return nonVisibleCcPairs.length > 0 ? (
                          <>
                            <Separator />
                            <h2 className="mb-1 font-medium text-base">
                              These connectors are not available to the{" "}
                              {user_teams && user_teams.length > 1
                                ? `group${
                                    props.values.groups.length > 1 ? "s" : ""
                                  } you have selected`
                                : "group you curate"}
                              :
                            </h2>
                            <p className="mb-3 text-sm">
                              Only connectors that are directly assigned to the
                              group you are trying to add the document set to
                              will be available.
                            </p>
                            <div className="mb-3 flex gap-2 flex-wrap">
                              {nonVisibleCcPairs.map((ccPair) => (
                                <div
                                  key={`${ccPair.connector.id}-${ccPair.credential.id}`}
                                  className="px-3 py-1 rounded-lg border border-non-selectable-border w-fit flex cursor-not-allowed"
                                >
                                  <div className="my-auto">
                                    <ConnectorTitle
                                      connector={ccPair.connector}
                                      ccPairId={ccPair.cc_pair_id}
                                      ccPairName={ccPair.name}
                                      isLink={false}
                                      showMetadata={false}
                                    />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </>
                        ) : null;
                      }}
                    />
                  </div>
                </>
              ) : (
                <div>
                  <h2 className="mb-1 font-medium text-base">
                    Pick your connectors:
                  </h2>
                  <p className="mb-3 text-xs">
                    All documents indexed by the selected connectors will be a part of this document set.
                  </p>

                  {/* Added the Search Box for filtering file connector */}
                  <input
                    type="text"
                    placeholder="Search connectors..."
                    className="mb-3 w-full px-3 py-2 text-sm border rounded-md"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />

                  <FieldArray
                    name="cc_pair_ids"
                    render={(arrayHelpers: ArrayHelpers) => {
                      // Apply privacy-based filtering first (based on document set privacy and team selection)
                      // Then apply search filtering for user convenience
                      const privacyFilteredCcPairs = getFilteredConnectors(ccPairs, props.values.is_public, props.values.selectedTeams || []);
                      const filteredCcPairs = privacyFilteredCcPairs.filter((ccPair) =>
                        (ccPair.name ?? "").toLowerCase().includes(searchQuery.toLowerCase())
                      );

                      return (
                        <div className="mb-3 flex gap-2 flex-wrap">
                          {filteredCcPairs.map((ccPair) => {
                            const ind = props.values.cc_pair_ids.indexOf(ccPair.cc_pair_id);
                            const isSelected = ind !== -1;

                            return (
                              <div
                                key={`${ccPair.connector.id}-${ccPair.credential.id}`}
                                className={
                                  `
                                  px-3 
                                  py-1
                                  rounded-lg 
                                  border
                                  border-border 
                                  w-fit 
                                  flex 
                                  cursor-pointer ` +
                                  (isSelected
                                    ? " bg-background-200"
                                    : " hover:bg-accent-background-hovered")
                                }
                                onClick={() => {
                                  if (isSelected) {
                                    arrayHelpers.remove(ind);
                                  } else {
                                    arrayHelpers.push(ccPair.cc_pair_id);
                                  }
                                }}
                              >
                                <div className="my-auto">
                                  <ConnectorTitle
                                    connector={ccPair.connector}
                                    ccPairId={ccPair.cc_pair_id}
                                    ccPairName={ccPair.name}
                                    isLink={false}
                                    showMetadata={false}
                                  />
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      );
                    }}
                  />
                </div>
              )}

              <div className="flex mt-6">
                <Button
                  type="submit"
                  variant="submit"
                  disabled={props.isSubmitting}
                  className="w-64 mx-auto"
                >
                  {isUpdate ? "Update!" : "Create!"}
                </Button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};
